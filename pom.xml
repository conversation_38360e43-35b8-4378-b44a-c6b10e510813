<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dcits.ec</groupId>
    <artifactId>ec-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.2-SNAPSHOT</version>

    <name>ec-parent</name>
    <description>Galaxy3技术平台-电子渠道-父项目</description>

    <modules>
        <module>ec-starter-parent</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>rbmp-release</id>
            <name>rbmp-release</name>
            <url>http://***********:8081/nexus/content/repositories/rbmp-release</url>
        </repository>
        <snapshotRepository>
            <id>rbmp-snapshots</id>
            <name>rbmp-snapshots</name>
            <url>http://***********:8081/nexus/content/repositories/rbmp-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <!--
         | 公共
        -->
        <java.encoding>UTF-8</java.encoding>
        <java.version>1.8</java.version>
        <disable.checks>false</disable.checks>
        <clirr.comparisonVersion>30</clirr.comparisonVersion>
        <formatter.config>eclipse-formatter-config-2space.xml</formatter.config>
        <maven.min-version>3.6.0</maven.min-version>
        <spotbugs.onlyAnalyze>true</spotbugs.onlyAnalyze>
        <resource.delimiter>@</resource.delimiter>
        <build-tools.version>1.1.8</build-tools.version>
        <checkstyle-core.version>8.21</checkstyle-core.version>
        <aspectjweaver.version>1.9.5</aspectjweaver.version>
        <fastjson.version>1.2.83</fastjson.version>

        <!--
         | maven参数配置
        -->
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.testTarget>${java.version}</maven.compiler.testTarget>
        <maven.compiler.testSource>${java.version}</maven.compiler.testSource>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <!-- 单元测试报告输出位置 -->
        <test.report.outputDirectory>target/allure-results</test.report.outputDirectory>
        <!-- 默认跳过单元测试 由具体模块决定是否进行单元测试-->
        <skipTests>true</skipTests>
        <!-- 单元测试失败是否继续 -->
        <testFailureIgnore>true</testFailureIgnore>
        <jacoco.exec.dataFile>target/jacoco.exec</jacoco.exec.dataFile>
        <jacoco.outputDirectory>target/jacoco-reports</jacoco.outputDirectory>
        <suiteXmlFile>src/test/resources/testng.xml</suiteXmlFile>
        <site.outputDirectory>target/site</site.outputDirectory>

        <!--
         | galaxy框架依赖模块版本
        -->
        <athena.version>4.3.3</athena.version>
        <comet-boot.version>3.7.0</comet-boot.version>
        <gravity-kit.version>3.5.0</gravity-kit.version>
        <gravity.version>3.4.0</gravity.version>

        <mars.version>4.7.0</mars.version>
        <sonic.version>4.7.0</sonic.version>
        <orbit.version>4.6.1</orbit.version>

        <libra.version>3.10.0</libra.version>
        <jupiter.version>3.9.0</jupiter.version>
        <jupiter-log-logback.version>4.8.0</jupiter-log-logback.version>
        <virgo.version>3.10.0</virgo.version>
        <saga.version>4.2.0</saga.version>
        <cloud-trace.version>1.9.1</cloud-trace.version>
        <comet-mybatis-generator.version>3.3.6</comet-mybatis-generator.version>
        <galaxy-assembly.version>3.2.1</galaxy-assembly.version>
        <!--
         | ec公共包版本管理
        -->
        <ec-parent.version>1.2-SNAPSHOT</ec-parent.version>
        <ec-base.version>1.2-SNAPSHOT</ec-base.version>
        <ec-comet.version>1.2-SNAPSHOT</ec-comet.version>
        <!--
         | 测试依赖模块版本
        -->
        <testng.version>7.1.0</testng.version>
        <!--
         | maven插件版本
        -->
        <maven-antrun-plugin.version>1.8</maven-antrun-plugin.version>
        <maven-assembly-plugin.version>3.3.0</maven-assembly-plugin.version>
        <maven-clean-plugin.version>3.1.0</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-dependency-plugin.version>3.1.1</maven-dependency-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-enforcer-plugin.version>3.0.0-M2</maven-enforcer-plugin.version>
        <maven-install-plugin.version>2.5.2</maven-install-plugin.version>
        <maven-jar-plugin.version>3.1.2</maven-jar-plugin.version>
        <maven-javadoc-plugin.version>3.0.1</maven-javadoc-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <maven-site-plugin.version>3.7.1</maven-site-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M4</maven-surefire-plugin.version>
        <versions-maven-plugin.version>2.7</versions-maven-plugin.version>
        <build-helper-maven-plugin.version>3.0.0</build-helper-maven-plugin.version>
        <xml-maven-plugin.version>1.0.2</xml-maven-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <modernizer-maven-plugin.version>1.7.1</modernizer-maven-plugin.version>
        <animal-sniffer-maven-plugin.version>1.17</animal-sniffer-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.0.0</maven-checkstyle-plugin.version>
        <clirr-maven-plugin.version>2.8</clirr-maven-plugin.version>
        <formatter-maven-plugin.version>2.8.1</formatter-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.5</jacoco-maven-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <spotbugs-maven-plugin.version>3.1.11</spotbugs-maven-plugin.version>
        <whitespace-maven-plugin.version>1.0.4</whitespace-maven-plugin.version>
        <findsecbugs-plugin.version>1.9.0</findsecbugs-plugin.version>
        <!-- Animal Sniffer Signature -->
        <signature.group>org.codehaus.mojo.signature</signature.group>
        <signature.artifact>java18</signature.artifact>
        <signature.version>1.0</signature.version>
        <exec-maven-plugin.version>3.0.0</exec-maven-plugin.version>

        <!--
         | 三方件版本
        -->
        <jmockit.version>1.49</jmockit.version>
        <minio.version>8.5.1</minio.version>
        <hutool-all.version>5.7.19</hutool-all.version>
        <io.qameta.allure.version>2.13.2</io.qameta.allure.version>
        <org.h2.version>1.4.177</org.h2.version>
        <com.caucho.version>4.0.63</com.caucho.version>
        <allure-maven.version>2.10.0</allure-maven.version>
        <org.jacoco.version>0.8.5</org.jacoco.version>
        <archaius-core.version>0.7.7</archaius-core.version>
        <org.hdrhistogram.version>2.1.12</org.hdrhistogram.version>
        <jcommander.version>1.78</jcommander.version>
        <objenesis.version>3.2</objenesis.version>
        <checker-qual.version>3.33.0</checker-qual.version>
        <error_prone_annotations.version>2.18.0</error_prone_annotations.version>
        <asm.version>6.2.1</asm.version>
        <jedis.version>3.8.0</jedis.version>
        <logback-classic.version>1.2.13</logback-classic.version>
        <logback-adapter.version>1.0.0</logback-adapter.version>
        <redisson.version>3.23.4</redisson.version>
        <thymeleaf.version>3.1.2.RELEASE</thymeleaf.version>
        <spring-kafka.version>2.9.11</spring-kafka.version>
        <snappy-java.version>1.1.10.4</snappy-java.version>
        <okio.version>3.4.0</okio.version>
        <netty-handler.version>4.1.100.Final</netty-handler.version>
        <jackson-core.version>2.15.2</jackson-core.version>
        <rxjava.version>3.1.7</rxjava.version>
        <nacos-client.version>2.2.2.1</nacos-client.version>

        <!--当前主流驱动集成-默认集成版本 -->
        <mariadb-java.version>3.1.0</mariadb-java.version>
        <dameng.version>8.1.1.49</dameng.version>
        <oceanbase.version>2.4.3</oceanbase.version>
        <kingbase.version>8.6.0</kingbase.version>
        <!--当前主流驱动集成-默认集成版本 -->

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-core</artifactId>
                <version>2.2.24.Final</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.dcits.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos-client.version}</version>
            </dependency>
            <dependency>
                <groupId>io.reactivex.rxjava3</groupId>
                <artifactId>rxjava</artifactId>
                <version>${rxjava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-handler.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty-handler.version}</version>
            </dependency>
            <dependency>
                <artifactId>okio</artifactId>
                <groupId>com.squareup.okio</groupId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <artifactId>snappy-java</artifactId>
                <groupId>org.xerial.snappy</groupId>
                <version>${snappy-java.version}</version>
            </dependency>
            <dependency>
                <artifactId>spring-kafka</artifactId>
                <groupId>org.springframework.kafka</groupId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf-spring6</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <artifactId>redisson</artifactId>
                <groupId>org.redisson</groupId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback-classic.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-classic.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>logback-adapter</artifactId>
                <version>${logback-adapter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits</groupId>
                <artifactId>comet-boot-dependencies</artifactId>
                <version>${comet-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--            galaxy-middleware-dependencies-->
            <dependency>
                <groupId>com.dcits.gravity</groupId>
                <artifactId>gravity-dependencies</artifactId>
                <version>${gravity.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.dcits.orbit</groupId>
                <artifactId>orbit-anno</artifactId>
                <version>${orbit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.orbit</groupId>
                <artifactId>orbit-redis-boot-starter</artifactId>
                <version>${orbit.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis-plus-boot-starter</artifactId>
                        <groupId>com.baomidou</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>druid-spring-boot-starter</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dcits.orbit</groupId>
                <artifactId>orbit-jetcache-support</artifactId>
                <version>${orbit.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jetcache-anno-api</artifactId>
                        <groupId>com.alicp.jetcache</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dcits.mars</groupId>
                <artifactId>mars-client-all</artifactId>
                <version>${mars.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis-plus-boot-starter</artifactId>
                        <groupId>com.baomidou</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dcits.virgo</groupId>
                <artifactId>virgo-client-all</artifactId>
                <version>${virgo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits.gravity</groupId>
                <artifactId>gravity-saga</artifactId>
                <version>${saga.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits.sonic</groupId>
                <artifactId>sonic-client-all</artifactId>
                <version>${sonic.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits.jupiter</groupId>
                <artifactId>jupiter-alibaba-starter</artifactId>
                <version>${jupiter.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.dcits.jupiter</groupId>
                <artifactId>jupiter-dependencies</artifactId>
                <version>${jupiter.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.dcits.jupiter</groupId>
                <artifactId>jupiter-spring-cloud-starter</artifactId>
                <version>${jupiter.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.dcits.jupiter</groupId>
                <artifactId>jupiter-log-logback</artifactId>
                <version>${jupiter-log-logback.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits</groupId>
                <artifactId>cloud-trace-starter</artifactId>
                <version>${cloud-trace.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.zipkin.brave</groupId>
                        <artifactId>brave-instrumentation-spring-rabbit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.amqp</groupId>
                        <artifactId>spring-amqp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.amqp</groupId>
                        <artifactId>spring-rabbit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dcits.libra</groupId>
                <artifactId>libra-client-spring-boot-starter</artifactId>
                <version>${libra.version}</version>
            </dependency>
            <!--            galaxy-middleware-dependencies-->

            <dependency>
                <groupId>com.dcits.luna.kit</groupId>
                <artifactId>gravity-kit</artifactId>
                <version>${gravity-kit.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits</groupId>
                <artifactId>comet-mybatis-generator</artifactId>
                <version>${comet-mybatis-generator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-batch</artifactId>
                <version>${ec-comet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-common</artifactId>
                <version>${ec-comet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-online-api</artifactId>
                <version>${ec-comet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-online-service</artifactId>
                <version>${ec-comet.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-common</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-yapi</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-galaxy-mars</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-mybatis</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-third-oss-api</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-third-oss-minio</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-third-oss-minio-starter</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-file-upload-oss-starter</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>snappy-java</artifactId>
                        <groupId>org.xerial.snappy</groupId>
                    </exclusion>
                </exclusions>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-base-dict-business</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-base-dict-online-api</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-base-dict-online-service</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-boot-system</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-boot-base</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-business-common-api</artifactId>
                <version>${ec-base.version}</version>
            </dependency>

            <!-- ec-comet-ig-product-->
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-boot-starter-ig-product-api</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-ig-product-api</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-ig-product-common</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-ig-product-flow-core</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-boot-starter-ig-product-batch</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-boot-autoconfigure-ig-product</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-boot-starter-ig-product-common</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId>ec-comet-boot-starter-ig-product-online</artifactId>
                <version>${ec-base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dcits.ec</groupId>
                <artifactId> ec-galaxy-middleware-dependencies</artifactId>
                <version>${ec-base.version}</version>
            </dependency>
            <!-- ec-comet-ig-product-->

            <!-- 三方件 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>${objenesis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>${checker-qual.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${error_prone_annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.archaius</groupId>
                <artifactId>archaius-core</artifactId>
                <version>${archaius-core.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>org.hdrhistogram</groupId>
                <artifactId>HdrHistogram</artifactId>
                <version>${org.hdrhistogram.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.beust</groupId>
                <artifactId>jcommander</artifactId>
                <version>${jcommander.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <!-- 三方件 -->

            <!--当前主流驱动集成-默认集成版本 -->
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>Dm8JdbcDriver18</artifactId>
                <version>${dameng.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kingbase8</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase.version}</version>
            </dependency>
            <!--当前主流驱动集成-默认集成版本 -->

        </dependencies>
    </dependencyManagement>

    <build>
        <!-- 插件管理， 里面的配置的插件版本可在子项目里使用 -->
        <pluginManagement>
            <plugins>
                <!-- 处理资源文件 -->
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <!-- 过滤后缀文件 -->
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                            <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                            <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                            <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <!-- maven内运行ant任务 -->
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                </plugin>
                <!-- 将依赖打进jar包 -->
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                    <configuration>
                        <recompressZippedFiles>false</recompressZippedFiles>
                    </configuration>
                </plugin>
                <!-- 清理插件 -->
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <!-- 编译插件 -->
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                </plugin>
                <!-- 发布到服务器 -->
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <!-- 人工处理依赖 -->
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                </plugin>
                <!-- 运行集成测试 -->
                <plugin>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                </plugin>
                <!-- 安装到本地仓库 -->
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
                <!-- 运行一组Maven项目并验证输出 -->
                <plugin>
                    <artifactId>maven-invoker-plugin</artifactId>
                    <version>${maven-invoker-plugin.version}</version>
                </plugin>
                <!-- 查看插件的详细信息 -->
                <plugin>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>${maven-help-plugin.version}</version>
                </plugin>
                <!-- 打成jar包 -->
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <!-- 生成javadoc -->
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                        <!-- 关闭非错误和警告信息 -->
                        <quiet>true</quiet>
                    </configuration>
                </plugin>


                <!-- 打成可执行jar -->
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
                <!-- 生成静态HTML网站 -->
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                    <!--<executions>-->
                    <!--<execution>-->
                    <!--<id>attach-descriptor</id>-->
                    <!--<goals>-->
                    <!--<goal>attach-descriptor</goal>-->
                    <!--</goals>-->
                    <!--</execution>-->
                    <!--</executions>-->
                    <!--<dependencies>-->
                    <!--<dependency>-->
                    <!--<groupId>org.apache.maven.skins</groupId>-->
                    <!--<artifactId>maven-fluido-skin</artifactId>-->
                    <!--<version>${fluido.version}</version>-->
                    <!--</dependency>-->
                    <!--</dependencies>-->
                </plugin>
                <!-- 打包source jar -->
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                </plugin>

                <!-- 执行单元测试用例 -->
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>

                <!-- 打成war包 -->
                <plugin>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>${maven-war-plugin.version}</version>
                </plugin>
                <!-- tag -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>taglist-maven-plugin</artifactId>
                    <version>${taglist-maven-plugin.version}</version>
                </plugin>
                <!-- 版本号管理 -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                </plugin>
                <!-- 添加许可证头文件 -->
                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${license-maven-plugin.version}</version>
                    <configuration>
                        <header>${project.basedir}/license.txt</header>
                        <excludes>
                            <exclude>**/*.ctrl</exclude>
                            <exclude>**/*.dat</exclude>
                            <exclude>**/*.lck</exclude>
                            <exclude>**/*.log</exclude>
                            <exclude>**/*maven-wrapper.properties</exclude>
                            <exclude>**/*MavenWrapperDownloader.java</exclude>
                            <exclude>**/*maven-wrapper.jar</exclude>
                            <exclude>.factorypath</exclude>
                            <exclude>.gitattributes</exclude>
                            <exclude>mvnw</exclude>
                            <exclude>mvnw.cmd</exclude>
                            <exclude>ICLA</exclude>
                            <exclude>LICENSE</exclude>
                            <exclude>KEYS</exclude>
                            <exclude>NOTICE</exclude>
                        </excludes>
                        <mapping>
                            <xml.vm>XML_STYLE</xml.vm>
                        </mapping>
                    </configuration>
                </plugin>
                <!-- 生成pdf -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-pdf-plugin</artifactId>
                    <version>${maven-pdf-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>pdf</id>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>pdf</goal>
                            </goals>
                            <configuration>
                                <includeReports>false</includeReports>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <!-- 为项目代码库打tag -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven-release-plugin.version}</version>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <mavenExecutorId>forked-path</mavenExecutorId>
                        <useReleaseProfile>false</useReleaseProfile>
                        <releaseProfiles>release</releaseProfiles>
                    </configuration>
                </plugin>
                <!-- 检测代码覆盖率 -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                </plugin>
                <!-- 代码格式化 -->
                <plugin>
                    <groupId>net.revelc.code.formatter</groupId>
                    <artifactId>formatter-maven-plugin</artifactId>
                    <version>${formatter-maven-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.github.hazendaz</groupId>
                            <artifactId>build-tools</artifactId>
                            <version>${build-tools.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <!-- 去掉结尾空格 -->
                <plugin>
                    <groupId>com.github.dantwining.whitespace-maven-plugin</groupId>
                    <artifactId>whitespace-maven-plugin</artifactId>
                    <version>${whitespace-maven-plugin.version}</version>
                </plugin>
                <!-- 检测使用已经被现代Java版本取代的遗留Java API -->
                <plugin>
                    <groupId>org.gaul</groupId>
                    <artifactId>modernizer-maven-plugin</artifactId>
                    <version>${modernizer-maven-plugin.version}</version>
                    <configuration>
                        <failOnViolations>false</failOnViolations>
                        <javaVersion>${maven.compiler.target}</javaVersion>
                    </configuration>
                </plugin>
                <!-- 精简POM -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${exec-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>xml-maven-plugin</artifactId>
                    <version>${xml-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-maven-plugin</artifactId>
                    <version>${flyway-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.infinispan</groupId>
                    <artifactId>infinispan-protocol-parser-generator-maven-plugin</artifactId>
                    <version>${infinispan-protocol-parser-generator-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.johnzon</groupId>
                    <artifactId>johnzon-maven-plugin</artifactId>
                    <version>${johnzon-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-maven-plugin</artifactId>
                    <version>${kotlin-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jooq</groupId>
                    <artifactId>jooq-codegen-maven</artifactId>
                    <version>${jooq-codegen-maven.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- Checkstyle 检查 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <configuration>
                            <skip>${disable.checks}</skip>
                            <configLocation>google_checks.xml</configLocation>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${checkstyle-core.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.github.hazendaz</groupId>
                        <artifactId>build-tools</artifactId>
                        <version>${build-tools.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- 代码静态检查 findbugs的继任者 -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${spotbugs-maven-plugin.version}</version>
                <configuration>
                    <plugins>
                        <plugin>
                            <groupId>com.h3xstream.findsecbugs</groupId>
                            <artifactId>findsecbugs-plugin</artifactId>
                            <version>${findsecbugs-plugin.version}</version>
                        </plugin>
                    </plugins>
                </configuration>
                <executions>
                    <execution>
                        <id>spotbugs-validation</id>
                        <phase>verify</phase>
                        <configuration>
                            <effort>Max</effort>
                            <threshold>Low</threshold>
                            <failOnError>true</failOnError>
                            <xmlOutput>true</xmlOutput>
                            <xmlOutputDirectory>${project.reporting.outputDirectory}</xmlOutputDirectory>
                            <spotbugsXmlOutputDirectory>${project.reporting.outputDirectory}
                            </spotbugsXmlOutputDirectory>
                            <onlyAnalyze>${spotbugs.onlyAnalyze}</onlyAnalyze>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- jar冲突检查 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <id>enforce-java</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <!-- jdk版本要求 -->
                                <requireJavaVersion>
                                    <message>
                                        <![CDATA[You are running an older version of Java. This application requires at least JDK ${java.version}.]]>
                                    </message>
                                    <version>[${maven.compiler.source},)</version>
                                </requireJavaVersion>
                                <!-- maven版本要求 -->
                                <requireMavenVersion>
                                    <message>
                                        <![CDATA[You are running an older version of Maven. This application requires at least Maven ${maven.min-version}.]]>
                                    </message>
                                    <version>[${maven.min-version},)</version>
                                </requireMavenVersion>
                                <!-- pom属性要求 -->
                                <requireProperty>
                                    <message>You must set the name attribute of the project</message>
                                    <property>project.name</property>
                                </requireProperty>
                                <requireProperty>
                                    <message>You must set the description attribute of the project</message>
                                    <property>project.description</property>
                                </requireProperty>
                                <!-- release版本不能依赖快照版本的jar包 -->
                                <requireReleaseDeps>
                                    <message>The release version is not allowed to depend on the snapshot version</message>
                                    <onlyWhenRelease>true</onlyWhenRelease>
                                </requireReleaseDeps>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 检查你的code是否使用了这些不在计划中的API -->
<!--            <plugin>-->
<!--                <groupId>org.codehaus.mojo</groupId>-->
<!--                <artifactId>animal-sniffer-maven-plugin</artifactId>-->
<!--                <version>${animal-sniffer-maven-plugin.version}</version>-->
<!--                <configuration>-->
<!--                    <signature>-->
<!--                        <groupId>${signature.group}</groupId>-->
<!--                        <artifactId>${signature.artifact}</artifactId>-->
<!--                        <version>${signature.version}</version>-->
<!--                    </signature>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>check-java-compat</id>-->
<!--                        <phase>process-classes</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

            <!-- 使用Clirr比较二进制文件或源的兼容性 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <version>${clirr-maven-plugin.version}</version>
                <configuration>
                    <comparisonVersion>${clirr.comparisonVersion}</comparisonVersion>
                    <failOnError>true</failOnError>
                    <failOnWarning>false</failOnWarning>
                </configuration>
            </plugin>

            <!-- 去掉结尾空格 -->
            <plugin>
                <groupId>com.github.dantwining.whitespace-maven-plugin</groupId>
                <artifactId>whitespace-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>trim</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 检测使用已经被现代Java版本取代的遗留Java API -->
            <plugin>
                <groupId>org.gaul</groupId>
                <artifactId>modernizer-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>modernizer</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>modernizer</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 添加module.name属性 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>regex-property</id>
                        <goals>
                            <goal>regex-property</goal>
                        </goals>
                        <configuration>
                            <name>module.name</name>
                            <value>${project.artifactId}</value>
                            <regex>-</regex>
                            <replacement>.</replacement>
                            <failIfNoMatch>false</failIfNoMatch>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 编译 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <parameters>true</parameters>
                </configuration>
            </plugin>

            <!-- 单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>${skipTests}</skipTests>
                    <encoding>${project.reporting.outputEncoding}</encoding>
                    <!--测试失败后，是否忽略并继续测试-->
                    <testFailureIgnore>${testFailureIgnore}</testFailureIgnore>
                    <suiteXmlFiles>
                        <suiteXmlFile>${suiteXmlFile}</suiteXmlFile>
                    </suiteXmlFiles>
                    <argLine>
                        @{argLine} -javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectjweaver.version}/aspectjweaver-${aspectjweaver.version}.jar"
                    </argLine>
                    <systemProperties>
                        <property>
                            <name>allure.results.directory</name>
                            <value>${test.report.outputDirectory}</value>
                        </property>
                    </systemProperties>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                        <version>${aspectjweaver.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- 测试覆盖率 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <dataFile>${jacoco.exec.dataFile}</dataFile>
                    <outputDirectory>${jacoco.outputDirectory}</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-init</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>