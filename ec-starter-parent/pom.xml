<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ec-parent</artifactId>
        <groupId>com.dcits.ec</groupId>
        <version>1.2-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ec-starter-parent</artifactId>
    <packaging>pom</packaging>

    <name>ec-starter-parent</name>
    <description>Galaxy3技术平台-电子渠道-启动父项目</description>

    <properties>
        <main.class>设置项目的主启动类</main.class>
        <final.name>${project.artifactId}</final.name>
    </properties>

    <build>
        <!-- 打包后的启动jar名称 -->
        <finalName>${final.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <classesDirectory>target/classes/</classesDirectory>
                    <archive>
                        <manifest>
                            <!-- 设置项目的主启动类 -->
                            <mainClass>${main.class}</mainClass>
                            <!-- 打包时 MANIFEST.MF文件不记录时间戳版本 -->
                            <useUniqueVersions>false</useUniqueVersions>
                            <addClasspath>true</addClasspath>
                            <!-- 依赖的jar的目录前缀 -->
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>conf/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <excludes>
                        <!-- 注意这玩意从编译结果目录开始算目录结构此处配置将jar内部依赖 -->
                        <exclude>lib/*</exclude>
                        <exclude>config/**</exclude>
                        <exclude>vmoptions/**</exclude>
                        <exclude>dev/**</exclude>
                        <exclude>sit/**</exclude>
                        <exclude>prd/**</exclude>
                        <exclude>uat/**</exclude>
                        <exclude>bin/**</exclude>
                        <exclude>logs/**</exclude>
                        <exclude>gravity/**</exclude>
                        <exclude>rpcConvert/**</exclude>
                        <exclude>*</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- 打包jar文件时，配置manifest文件，加入lib包的jar依赖 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <type>jar</type>
                            <includeTypes>jar</includeTypes>
                            <outputDirectory>
                                ${project.build.directory}/lib
                            </outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>unpack</id>
                        <phase>package</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <!-- 关联Galaxy统一管理启动脚本 -->
                                    <groupId>com.dcits.galaxy</groupId>
                                    <artifactId>galaxy-linux-assembly</artifactId>
                                    <version>${galaxy-assembly.version}</version>
                                    <outputDirectory>${project.basedir}/target/scripts</outputDirectory>
                                    <includes>**</includes>
                                    <!--<includes>META-INF/assembly/**</includes>-->
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <!-- The configuration of the plugin -->
                <configuration>
                    <finalName>${final.name}</finalName>
                    <!-- Specifies the configuration file of the assembly plugin -->
                    <descriptors>
                        <descriptor>target/scripts/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <!--配置luna-profile的gravity文件到启动应用的fatjar中，相对路径需要根据工程实际情况进行调整-->
            <resource>
              <directory>${project.basedir}/../../luna-profiles</directory>
              <includes>
                <include>gravity/**</include>
              </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>